import { TSubscriptionPeriodSlug } from "./../TSubscriptionPeriodSlug";
import type { SubscriptionStatusName } from "@/constants/subscription_status_name";
import type { SubscriptionStatusCode } from "@/constants/subscription_status_code";

export type TSubscriptionInfoResource = {
  status: SubscriptionStatusCode;
  status_name: SubscriptionStatusName;
  user_account_id: number;
  cards_limit?: number;
  cards_count: number;
  cards_blocked_count: number;
  cards_active_count: number;
  amount: string;
  ordered_next: string;
  cashback_percent: string;
  cashback_limit: string;
  fee_topup: string;
  hold_cashback: string;
  approved_cashback: string;
  tariff_name: string;
  billing_period: TSubscriptionPeriodSlug;
};
