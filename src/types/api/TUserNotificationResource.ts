import type { TCardTariffSlug } from "@/composable/Tariff";
import { IsoCodeNames } from "@/constants/iso_code_names";

export type TUserNotificationResourceBase = {
  notification: {
    id: number;
    notify_type_id: number;
    text: string;
    priority: string;
    buttons: string[] | null;
    read_at: string | null;
    created_at: string;
  };
  user: {
    uuid: string;
  };
  meta: {
    card_id: string;
    account_id: string;
    amount: string;
    link: string;
  };
};

export type TUserNotificationPayload = {
  page?: number;
  priority?: string;
};

export enum TNotificationSlug {
  VERIFICATION_TRY = "verification-try",
  VERIFICATION_RESTRICT = "verification-restrict",
  VERIFICATION_SUCCESS = "verification-success",
  VERIFICATION_FAIL = "verification-fail",
  NEW_LOGIN = "new-login",
  CARD_AUTO_REFILL_FAILED = "card-auto-refill-failed",
  NOT_ENOUGH_MONEY_FOR_SUBSCRIPTION = "not-enough-money-for-subscription",
  TRANSACTION_DECLINED = "transaction-declined",
  WITHDRAWAL_APPROVED = "withdrawal-approved",
  WITHDRAWAL_DECLINED = "withdrawal-declined",
  TWO_FACTOR_AUTHENTICATION_ENABLED = "two-factor-authentication-enabled",
  OVERDRAFT = "overdraft",
  CARD_TO_MIN_SUM = "card-to-min-sum",
  BLOCKED = "blocked",
  REORDER_FAIL = "reorder-fail",
  WILL_END_SOON = "will-end-soon",
  THREEDSCODE_RECEIVED = "threedscode-received",
  BONUS_BALANCE = "promo-5-usd-bonus-balance",
  BONUS_BALANCE_REMINDER = "promo-5-usd-bonus-balance-reminder",
}

export type TDefaultCardNotificationEventData = {
  card_id: string;
  card_mask: string;
  card_mask4: string;
  tariff_id: string;
  tariff_slug: TCardTariffSlug;
  card_description: string | null;
};

export type TVerificationTryNotificationEventData = {
  verification_step?: string;
};

export type TCardToMinSumNotificationEventData = {
  currency: IsoCodeNames;
  amount: string;
} & TDefaultCardNotificationEventData;

export type TVerificationRestrictNotificationEventData = {
  verification_step?: string;
};

export type TVerificationSuccessNotificationEventData = {
  allow_manager?: string;
  card_deposit_limit?: string;
  card_limit?: string;
  name?: string;
  slug?: string;
};

export type TVerificationFailNotificationEventData = {
  verification_step?: string;
};

export type TNewLoginNotificationEventData = {
  ip?: string;
  os?: string;
  geo?: string;
  date?: string;
  token_id: string;
  platform?: string;
  user_agent?: string;
};

export type TCardAutoRefillFailedNotificationEventData = {
  usdt_account_id: string;
} & TDefaultCardNotificationEventData;

export type TNotEnoughMoneyForSubscriptionNotificationEventData = {
  amount?: string;
  days?: string;
  tariff?: string;
  user_account_id?: string;
};

export type TTransactionDeclinedNotificationEventData = {
  card_system?: string;
  amount?: string;
  processed_at?: string;
  reason?: string;
  merchant?: string;
} & TDefaultCardNotificationEventData;

export type TWithdrawalApprovedNotificationEventData = {
  address?: string;
  amount?: string;
  currency?: string;
  link?: string;
  tx_id?: string;
};

export type TWithdrawalDeclinedNotificationEventData = {
  address?: string;
  amount?: string;
  comment?: string;
  currency?: string;
};

export type TTwoFactorAuthenticationEnabledEventData = {
  provider?: string;
};

export type TOverdraftEventData = {
  balance?: string;
  minimal?: string;
} & TDefaultCardNotificationEventData;

export type TBlockedEventData = TDefaultCardNotificationEventData;

export type TReorderFailEventData = TDefaultCardNotificationEventData;

export type TWillEndSoonEventData = TDefaultCardNotificationEventData;

export type TThreedsCodeReceivedEventData = TDefaultCardNotificationEventData;

export type TBonusBalanceEventData = {};

export type TBonusBalanceReminderEventData = {
  expired_at?: string;
};

export type TNotificationType<Slug, EventData> =
  TUserNotificationResourceBase & {
    slug: Slug;
    event_data: EventData;
  };

export type TVerificationTryData = TNotificationType<
  TNotificationSlug.VERIFICATION_TRY,
  TVerificationTryNotificationEventData
>;

export type TVerificationRestrictData = TNotificationType<
  TNotificationSlug.VERIFICATION_RESTRICT,
  TVerificationRestrictNotificationEventData
>;

export type TVerificationSuccessData = TNotificationType<
  TNotificationSlug.VERIFICATION_SUCCESS,
  TVerificationSuccessNotificationEventData
>;

export type TVerificationFailData = TNotificationType<
  TNotificationSlug.VERIFICATION_FAIL,
  TVerificationFailNotificationEventData
>;

export type TNewLoginNotificationData = TNotificationType<
  TNotificationSlug.NEW_LOGIN,
  TNewLoginNotificationEventData
>;

export type TCardAutoRefillFailedNotificationData = TNotificationType<
  TNotificationSlug.CARD_AUTO_REFILL_FAILED,
  TCardAutoRefillFailedNotificationEventData
>;

export type TNotEnoughMoneyForSubscriptionNotificationData = TNotificationType<
  TNotificationSlug.NOT_ENOUGH_MONEY_FOR_SUBSCRIPTION,
  TNotEnoughMoneyForSubscriptionNotificationEventData
>;

export type TTransactionDeclinedNotificationData = TNotificationType<
  TNotificationSlug.TRANSACTION_DECLINED,
  TTransactionDeclinedNotificationEventData
>;

export type TWithdrawalApprovedNotificationData = TNotificationType<
  TNotificationSlug.WITHDRAWAL_APPROVED,
  TWithdrawalApprovedNotificationEventData
>;

export type TWithdrawalDeclinedNotificationData = TNotificationType<
  TNotificationSlug.WITHDRAWAL_DECLINED,
  TWithdrawalDeclinedNotificationEventData
>;

export type TTwoFactorAuthenticationEnabledData = TNotificationType<
  TNotificationSlug.TWO_FACTOR_AUTHENTICATION_ENABLED,
  TTwoFactorAuthenticationEnabledEventData
>;

export type TOverdraftData = TNotificationType<
  TNotificationSlug.OVERDRAFT,
  TOverdraftEventData
>;

export type TCardToMinSumData = TNotificationType<
  TNotificationSlug.CARD_TO_MIN_SUM,
  TCardToMinSumNotificationEventData
>;

export type TBlockedData = TNotificationType<
  TNotificationSlug.BLOCKED,
  TBlockedEventData
>;

export type TReorderFailData = TNotificationType<
  TNotificationSlug.REORDER_FAIL,
  TReorderFailEventData
>;

export type TWillEndSoonData = TNotificationType<
  TNotificationSlug.WILL_END_SOON,
  TWillEndSoonEventData
>;

export type TThreedsCodeReceivedData = TNotificationType<
  TNotificationSlug.THREEDSCODE_RECEIVED,
  TThreedsCodeReceivedEventData
>;

export type TBonusBalanceData = TNotificationType<
  TNotificationSlug.BONUS_BALANCE,
  TBonusBalanceEventData
>;

export type TBonusBalanceReminderData = TNotificationType<
  TNotificationSlug.BONUS_BALANCE_REMINDER,
  TBonusBalanceReminderEventData
>;

export type TUserNotificationResource =
  | TVerificationTryData
  | TVerificationRestrictData
  | TVerificationSuccessData
  | TVerificationFailData
  | TNewLoginNotificationData
  | TCardAutoRefillFailedNotificationData
  | TNotEnoughMoneyForSubscriptionNotificationData
  | TTransactionDeclinedNotificationData
  | TWithdrawalApprovedNotificationData
  | TWithdrawalDeclinedNotificationData
  | TTwoFactorAuthenticationEnabledData
  | TOverdraftData
  | TCardToMinSumData
  | TBlockedData
  | TReorderFailData
  | TWillEndSoonData
  | TThreedsCodeReceivedData
  | TBonusBalanceData
  | TBonusBalanceReminderData;
