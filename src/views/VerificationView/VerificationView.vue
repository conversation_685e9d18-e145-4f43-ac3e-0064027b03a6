<script setup lang="ts">
import VerificationProcess from "@/components/VerificationProcess/VerificationProcess.vue";
import AvailableVerification from "@/components/AvailableVerification/AvailableVerification.vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import router from "@/router";
import { RouteName } from "@/constants/route_name";
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import VerificationApproved from "@/components/VerificationApproved/VerificationApproved.vue";
import { useVerificationState } from "@/composable";
import UITransition from "@/components/ui/UITransition.vue";

const { t } = useI18n();
const { verificationActualData, nextVerificationData } = useVerificationState();

type TVerificationStates = "available" | "progress" | "approved";
const currentState = ref<TVerificationStates>("available");

const onClose = () => {
  router.push({ name: RouteName.DASHBOARD });
};

const upgradeHandle = () => {
  currentState.value = "progress";
};

const onSuccessHandle = () => {
  currentState.value = "approved";
};

const title = computed(() => {
  if (currentState.value === "available") return "";

  return t("verification.verification");
});

const subtitle = computed(() => {
  const subtitles: Record<TVerificationStates, string> = {
    available: "",
    progress: nextVerificationData.value?.name ?? "",
    approved: verificationActualData.value?.data?.step ?? "",
  };

  return subtitles[currentState.value];
});
</script>

<template>
  <div>
    <UIFullScreenModal
      :title="title"
      :subtitle="subtitle"
      is-open
      @close="onClose">
      <template #content>
        <div>
          <UITransition name="fade-slide-up">
            <AvailableVerification
              v-if="currentState === 'available'"
              @close="onClose"
              @upgrade="upgradeHandle" />

            <VerificationProcess
              v-else-if="currentState === 'progress'"
              @success="onSuccessHandle" />

            <VerificationApproved
              v-else-if="currentState === 'approved'"
              @increase-limit="upgradeHandle"
              @close="onClose" />
          </UITransition>
        </div>
      </template>
    </UIFullScreenModal>
  </div>
</template>
