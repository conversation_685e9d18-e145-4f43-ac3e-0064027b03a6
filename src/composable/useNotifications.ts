import {
  useBusinessBalanceRequestsGet,
  useUserNotificationGet,
  useUserNotificationNewGet,
} from "@/composable";
import config from "@/config/env";
import { useWebSocket } from "@/helpers/webSoket";
import type { TUserNotificationResource } from "@/types/api/TUserNotificationResource";
import { ref } from "vue";
import { useUserStore } from "@/stores/user";
import type { TMasterBalanceRequestResource } from "@/types/api/TMasterBalanceRequestResource";
import { getMoneyRequestStatusByValue } from "@/helpers/store";

const notificationsTotal = ref(0);
const unreadNotificationsTotal = ref(0);
const activeMoneyRequestsTotal = ref(0);

export const useNotifications = () => {
  const userStore = useUserStore();

  const currentPage = ref(1);
  const isNotificationsLoading = ref(false);
  const notifications = ref<TUserNotificationResource[]>([]);
  const unreadNotifications = ref<TUserNotificationResource[]>([]);
  const critical = ref<TUserNotificationResource[]>([]);
  const canGetMoreNotifications = ref(true);
  const activeMoneyRequests = ref<TMasterBalanceRequestResource[]>([]);
  const isRequestsLoading = ref(false);

  const resetNotificationsData = () => {
    currentPage.value = 1;
    canGetMoreNotifications.value = true;
    notifications.value = [];
    unreadNotifications.value = [];
    activeMoneyRequests.value = [];
  };

  const getNotifications = async () => {
    if (isNotificationsLoading.value || !canGetMoreNotifications.value) return;

    isNotificationsLoading.value = true;

    try {
      const { data: notificationsData } = await useUserNotificationGet(
        {
          page: currentPage.value,
        },
        {},
        0
      );

      notifications.value = [
        ...notifications.value,
        ...(notificationsData.value?.data ?? []),
      ];

      unreadNotifications.value = notifications.value.filter(
        (notification) => !notification.notification.read_at
      );
      unreadNotificationsTotal.value = unreadNotifications.value.length;

      notificationsTotal.value = notificationsData.value?.meta.total ?? 0;
      currentPage.value += 1;

      if (
        notificationsData.value &&
        notificationsData.value.meta.current_page >=
          notificationsData.value.meta.last_page
      ) {
        canGetMoreNotifications.value = false;
      }
    } catch (ex) {
      console.error(
        "Notifications->getNotifications->catch error handled: ",
        ex
      );
    }

    isNotificationsLoading.value = false;
  };

  const readNotifications = () => {
    useUserNotificationNewGet();
  };

  const getMoneyRequests = async () => {
    if (!userStore.isTeamOwner) return;

    isRequestsLoading.value = true;

    const { data: moneyRequestsData } = await useBusinessBalanceRequestsGet(
      {
        count: 100,
        page: 1,
      },
      {},
      0
    );

    if (moneyRequestsData.value?.data) {
      activeMoneyRequests.value = moneyRequestsData.value.data.filter(
        (request) =>
          String(request.status) === getMoneyRequestStatusByValue("created")
      );

      activeMoneyRequestsTotal.value = activeMoneyRequests.value.length;
    }

    isRequestsLoading.value = false;
  };

  if (config.wsNotificationsUrl) {
    useWebSocket({
      url: `${config.wsNotificationsUrl}uuid=${userStore.user.uuid}`,
      onMessage: (data) => {
        if (data.action === "notification" && notifications.value.length < 25) {
          const notification = data.data;

          notifications.value = [notification, ...notifications.value];
        }

        if (data?.action === "notification") {
          if (data?.data?.notification?.priority === "critical") {
            critical.value.push(data.data);
          }
        }
      },
    });
  }

  return {
    currentPage,
    notifications,
    notificationsTotal,
    isNotificationsLoading,
    unreadNotifications,
    activeMoneyRequests,
    isRequestsLoading,
    getNotifications,
    getMoneyRequests,
    readNotifications,
    resetNotificationsData,
    unreadNotificationsTotal,
    activeMoneyRequestsTotal,
  };
};
