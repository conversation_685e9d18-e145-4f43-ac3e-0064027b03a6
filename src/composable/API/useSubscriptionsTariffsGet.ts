import { useGetRequest } from "@/composable/useGetRequest";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import type { TSubscriptionPeriodSlug } from "@/types/TSubscriptionPeriodSlug";
import type { MaybeRef, UseFetchOptions } from "@vueuse/core";

export type TUseSubscriptionsTariffsGetQuery = {
  renewal?: TSubscriptionPeriodSlug;
};

export const useSubscriptionsTariffsGet = (
  options: UseFetchOptions = {},
  queryConfig: MaybeRef<TUseSubscriptionsTariffsGetQuery> = {}
) => {
  return useGetRequest<
    { data: TSubscriptionTariffResource[] | null },
    TUseSubscriptionsTariffsGetQuery
  >("subscriptions/tariffs", queryConfig, options);
};
