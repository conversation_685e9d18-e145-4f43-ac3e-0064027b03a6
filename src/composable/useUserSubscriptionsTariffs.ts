import { computed } from "vue";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import { cond, matches, stubTrue } from "lodash";
import { useSubscriptionsTariffs } from "@/composable/useSubscriptionsTariffs";
import { useNewSubscriptionsExperiment } from "@/composable/useNewSubscriptionsExperiment";
import { useNewSubscriptionsExperimentTwo } from "@/composable/useNewSubscriptionsExperimentTwo";
import { useSubscriptionsExperimentRetentionUsers } from "@/composable/useSubscriptionsExperimentRetentionUsers";
import { STANDARD_TARIFFS } from "@/constants/standard_tariffs";

/**
 * Composable to manage and retrieve user subscription tariffs based on experiments conditions.
 *
 * @category Composables
 */
export function useUserSubscriptionsTariffs() {
  const { getValue } = useNewSubscriptionsExperiment();
  const { isActive: isActiveNewSubscriptionsExperiment1 } = getValue();

  const { getValue: getValueTwo } = useNewSubscriptionsExperimentTwo();
  const { isActive: isActiveNewSubscriptionsExperimentTwo } = getValueTwo();

  // Retention Users Experiment data
  const { getValue: getValueRetentionUsers } =
    useSubscriptionsExperimentRetentionUsers();
  const {
    isActive: isActiveSubscriptionsExperimentRetentionUsers,
    isPending: isRetentionUsersPending,
  } = getValueRetentionUsers();

  const { subscriptionsTariffs, isFetching } = useSubscriptionsTariffs();

  const getStandardTariffs = (tariffs: TSubscriptionTariffResource[]) =>
    tariffs.filter((item) =>
      STANDARD_TARIFFS.includes(item.slug.toLowerCase())
    );

  const getVariantATariffs = (tariffs: TSubscriptionTariffResource[]) =>
    tariffs.filter(
      (item) => item.slug.includes("_A") || item.slug === "Extra Small"
    );

  const getVariantBTariffs = (tariffs: TSubscriptionTariffResource[]) =>
    tariffs.filter((item) => item.slug.includes("_B"));

  const userSubscriptionsTariffs = computed<TSubscriptionTariffResource[]>(
    () => {
      const tariffs = (subscriptionsTariffs.value ?? []).filter(
        (item) => item.status_name === "ACTIVE" && item.type_name === "MAIN"
      );
      const exp1 = isActiveNewSubscriptionsExperiment1.value;
      const exp2 = isActiveNewSubscriptionsExperimentTwo.value;
      const exp3 = isActiveSubscriptionsExperimentRetentionUsers.value;

      const getUserSubscriptionTariffs = cond([
        // Active Experiment 3 ignores all other experiments
        [matches({ exp3: 1 }), () => getVariantATariffs(tariffs)],
        [matches({ exp1: 0, exp2: false }), () => getStandardTariffs(tariffs)],
        [matches({ exp1: 2, exp2: false }), () => getStandardTariffs(tariffs)],
        [matches({ exp1: 1, exp2: false }), () => getVariantATariffs(tariffs)],
        [
          matches({ exp1: false, exp2: false }),
          () => getVariantATariffs(tariffs),
        ],
        [matches({ exp1: false, exp2: 0 }), () => getVariantATariffs(tariffs)],
        [matches({ exp1: false, exp2: 1 }), () => getVariantBTariffs(tariffs)],
        [
          matches({ exp1: false, exp2: false }),
          () => getVariantATariffs(tariffs),
        ],
        [
          matches({ exp1: false, exp2: false }),
          () => getVariantATariffs(tariffs),
        ],
        [stubTrue, () => getVariantATariffs(tariffs)],
      ]);

      return getUserSubscriptionTariffs({ exp1, exp2, exp3 }).sort(
        (a, b) => a.cards_limit - b.cards_limit
      );
    }
  );

  /**
   * get max value of fieldName from userSubscriptionsTariffs
   * @param fieldName "cashback_limit" or "cashback_percent"
   * @returns Integer
   */
  const getTariffFieldMaxValue = (
    fieldName: "cashback_limit" | "cashback_percent"
  ) => {
    const maxTariff = userSubscriptionsTariffs.value?.sort(
      (a, b) => parseInt(b[fieldName]) - parseInt(a[fieldName])
    )[0];

    return maxTariff ? parseInt(maxTariff[fieldName]) : 0;
  };

  /**
   * get max value of cashback_limit field from tariffs
   * @returns Integer
   */
  const maxCashbackLimit = computed(() =>
    getTariffFieldMaxValue("cashback_limit")
  );

  /**
   * get max value of cashback_percent field from tariffs
   * @returns Integer
   */
  const maxCashbackPercent = computed(() =>
    getTariffFieldMaxValue("cashback_percent")
  );

  /**
   * get max value of cards_limit field from tariffs
   * @returns Integer
   */
  const maxCardsLimit = computed(() => {
    const FIELD_NAME = "cards_limit";
    const maxTariff = userSubscriptionsTariffs.value?.sort(
      (a, b) => b[FIELD_NAME] - a[FIELD_NAME]
    )[0];

    return maxTariff ? maxTariff[FIELD_NAME] : 0;
  });

  /**
   * get min value of cashback_percent field in userSubscriptionsTariffs
   * @returns Integer (default = 10)
   */
  const minAmountFirst = computed(() => {
    const DEFAULT_MIN_AMOUNT_FIRST = 10;
    const minTariff = userSubscriptionsTariffs.value?.sort(
      (a, b) => parseFloat(a.amount_first) - parseFloat(b.amount_first)
    )[0];

    return minTariff
      ? parseInt(minTariff.amount_first)
      : DEFAULT_MIN_AMOUNT_FIRST;
  });
  /**
   * get min value of fee_topup field in userSubscriptionsTariffs
   * @returns Integer (default = 3)
   */
  const minFeeTopup = computed(() => {
    const DEFAULT_FEE_TOPUP = 3;
    const minTariff = userSubscriptionsTariffs.value?.sort(
      (a, b) => parseFloat(a.fee_topup) - parseFloat(b.fee_topup)
    )[0];

    return minTariff ? parseInt(minTariff.fee_topup) : DEFAULT_FEE_TOPUP;
  });

  return {
    userSubscriptionsTariffs,
    isActiveNewSubscriptionsExperiment1,
    isActiveNewSubscriptionsExperimentTwo,
    isActiveSubscriptionsExperimentRetentionUsers,
    isRetentionUsersPending,
    isFetching,
    maxCashbackLimit,
    maxCashbackPercent,
    maxCardsLimit,
    minAmountFirst,
    minFeeTopup,
  };
}
