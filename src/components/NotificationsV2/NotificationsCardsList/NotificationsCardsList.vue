<script lang="ts" setup>
// eslint-disable-next-line max-len
import NotificationsCardsFactory from "@/components/NotificationsV2/NotificationsCardsFactory/NotificationsCardsFactory.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import { useUserAccounts } from "@/composable";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import { type TUserNotificationResource } from "@/types/api/TUserNotificationResource";
import { useIntersectionObserver } from "@vueuse/core";
import { isToday } from "date-fns";
import { ref, computed } from "vue";

const props = defineProps<{
  items: TUserNotificationResource[];
  isLoadingMore: boolean;
}>();

const emit = defineEmits<{
  "load-more": [];
}>();

const { formatDate, t } = useI18nWrapper();
const { usdtAccount } = useUserAccounts({ immediate: false });

const endTrackerRef = ref<HTMLDivElement>();

useIntersectionObserver(endTrackerRef, ([entry]) => {
  if (entry.isIntersecting) {
    emit("load-more");
  }
});

const groupedNotifications = computed(() => {
  const groups = props.items.reduce((acc, item) => {
    const date = new Date(item.notification.created_at);
    const key = isToday(date)
      ? t("common.today")
      : formatDate(date, "dd MMM yyyy");
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);

    return acc;
  }, {} as Record<string, TUserNotificationResource[]>);

  return Object.entries(groups).map(([dateKey, items]) => ({ dateKey, items }));
});
</script>

<template>
  <div class="flex flex-col gap-5">
    <div
      v-for="group in groupedNotifications"
      :key="group.dateKey">
      <UI2Text
        class="mb-3"
        type="body-s">
        {{ group.dateKey }}
      </UI2Text>

      <div class="flex flex-col gap-2">
        <NotificationsCardsFactory
          v-for="data in group.items"
          :key="data.notification.id"
          :usdt-account-id="usdtAccount?.id ?? null"
          :data="data" />
      </div>
    </div>

    <Loader v-if="isLoadingMore" />
    <div
      :key="props.items.length"
      ref="endTrackerRef"
      class="h-px -mt-5"></div>
  </div>
</template>
