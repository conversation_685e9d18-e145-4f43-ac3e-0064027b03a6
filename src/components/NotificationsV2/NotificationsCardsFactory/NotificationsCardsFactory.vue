<script lang="ts" setup>
import { computed } from "vue";
import type { Component } from "vue";
import {
  TNotificationSlug,
  type TUserNotificationResource,
} from "@/types/api/TUserNotificationResource";
import { useModalStack } from "@/composable";
import { NOTIFICATIONS_WITHOUT_DETAILS } from "@/components/NotificationsV2/NotificationsCardsFactory/constants";
/* eslint-disable max-len */
import NotificationsVerificationTryPreview from "@/components/NotificationsV2/NotificationsVerificationTry/NotificationsVerificationTryPreview.vue";
import NotificationsVerificationRestrictPreview from "@/components/NotificationsV2/NotificationsVerificationRestrict/NotificationsVerificationRestrictPreview.vue";
import NotificationsVerificationSuccessPreview from "@/components/NotificationsV2/NotificationsVerificationSuccess/NotificationsVerificationSuccessPreview.vue";
import NotificationsVerificationFailPreview from "@/components/NotificationsV2/NotificationsVerificationFail/NotificationsVerificationFailPreview.vue";
import NotificationsNewLoginPreview from "@/components/NotificationsV2/NotificationsNewLogin/NotificationsNewLoginPreview.vue";
import NotificationsCardAutoRefillFailedPreview from "@/components/NotificationsV2/NotificationsCardAutoRefillFailed/NotificationsCardAutoRefillFailedPreview.vue";
import NotificationsNotEnoughMoneyForSubscriptionPreview from "@/components/NotificationsV2/NotificationsNotEnoughMoneyForSubscription/NotificationsNotEnoughMoneyForSubscriptionPreview.vue";
import NotificationsTransactionDeclinedPreview from "@/components/NotificationsV2/NotificationsTransactionDeclined/NotificationsTransactionDeclinedPreview.vue";
import NotificationWithdrawalApprovedPreview from "@/components/NotificationsV2/NotificationWithdrawalApproved/NotificationWithdrawalApprovedPreview.vue";
import NotificationWithdrawalDeclinedPreview from "@/components/NotificationsV2/NotificationWithdrawalDeclined/NotificationWithdrawalDeclinedPreview.vue";
import NotificationsTwoFactorAuthenticationEnabledPreview from "@/components/NotificationsV2/NotificationsTwoFactorAuthenticationEnabled/NotificationsTwoFactorAuthenticationEnabledPreview.vue";
import NotificationsDefaultPreview from "@/components/NotificationsV2/NotificationsDefault/NotificationsDefaultPreview.vue";
import NotificationsOverdraftPreview from "@/components/NotificationsV2/NotificationsOverdraft/NotificationsOverdraftPreview.vue";
import NotificationsCardToMinSumPreview from "@/components/NotificationsV2/NotificationsCardToMinSum/NotificationsCardToMinSumPreview.vue";
import NotificationsBlockedPreview from "@/components/NotificationsV2/NotificationsBlocked/NotificationsBlockedPreview.vue";
import NotificationsReorderFailPreview from "@/components/NotificationsV2/NotificationsReorderFail/NotificationsReorderFailPreview.vue";
import NotificationsBonusBalancePreview from "@/components/NotificationsV2/NotificationsBonusBalance/NotificationsBonusBalancePreview.vue";
import NotificationsBonusBalanceReminderPreview from "@/components/NotificationsV2/NotificationsBonusBalanceReminder/NotificationsBonusBalanceReminderPreview.vue";
/* eslint-enable max-len */

const props = defineProps<{
  data: TUserNotificationResource;
  usdtAccountId: number | null;
}>();

const { openModal } = useModalStack();

const component = computed<Component | null>(() => {
  switch (props.data.slug) {
    case TNotificationSlug.VERIFICATION_TRY:
      return NotificationsVerificationTryPreview;
    case TNotificationSlug.VERIFICATION_RESTRICT:
      return NotificationsVerificationRestrictPreview;
    case TNotificationSlug.VERIFICATION_SUCCESS:
      return NotificationsVerificationSuccessPreview;
    case TNotificationSlug.VERIFICATION_FAIL:
      return NotificationsVerificationFailPreview;
    case TNotificationSlug.NEW_LOGIN:
      return NotificationsNewLoginPreview;
    case TNotificationSlug.CARD_AUTO_REFILL_FAILED:
      return NotificationsCardAutoRefillFailedPreview;
    case TNotificationSlug.NOT_ENOUGH_MONEY_FOR_SUBSCRIPTION:
      return NotificationsNotEnoughMoneyForSubscriptionPreview;
    case TNotificationSlug.TRANSACTION_DECLINED:
      return NotificationsTransactionDeclinedPreview;
    case TNotificationSlug.WITHDRAWAL_APPROVED:
      return NotificationWithdrawalApprovedPreview;
    case TNotificationSlug.WITHDRAWAL_DECLINED:
      return NotificationWithdrawalDeclinedPreview;
    case TNotificationSlug.TWO_FACTOR_AUTHENTICATION_ENABLED:
      return NotificationsTwoFactorAuthenticationEnabledPreview;
    case TNotificationSlug.OVERDRAFT:
      return NotificationsOverdraftPreview;
    case TNotificationSlug.CARD_TO_MIN_SUM:
      return NotificationsCardToMinSumPreview;
    case TNotificationSlug.BLOCKED:
      return NotificationsBlockedPreview;
    case TNotificationSlug.REORDER_FAIL:
      return NotificationsReorderFailPreview;
    case TNotificationSlug.BONUS_BALANCE:
      return NotificationsBonusBalancePreview;
    case TNotificationSlug.BONUS_BALANCE_REMINDER:
      return NotificationsBonusBalanceReminderPreview;
    default:
      return null;
  }
});

const isNewNotification = computed<boolean>(() => {
  const isEventData = Boolean(props.data.event_data);

  /**
   * Checks that current notification data is explicitly typed.
   * `true` means that notification has new data structure
   */
  const isTypedNotification = Object.values(TNotificationSlug).includes(
    props.data.slug
  );

  return isEventData && isTypedNotification;
});

const handleCardClick = () => {
  if (
    !isNewNotification.value ||
    NOTIFICATIONS_WITHOUT_DETAILS.includes(props.data.slug)
  )
    return;

  openModal("notificationDetailModal", { data: props.data });
};
</script>
<template>
  <component
    :is="component"
    v-if="isNewNotification"
    :created-at="props.data.notification.created_at"
    :read-at="props.data.notification.read_at"
    :data="props.data.event_data"
    @click="handleCardClick" />

  <NotificationsDefaultPreview
    v-else
    :data="props.data"
    :created-at="props.data.notification.created_at"
    :read-at="props.data.notification.read_at"
    :usdt-account-id="props.usdtAccountId" />
</template>
