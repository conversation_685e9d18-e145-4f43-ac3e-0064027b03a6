<script setup lang="ts">
import type { TBonusBalanceEventData } from "@/types/api/TUserNotificationResource";
import NotificationsCard from "@/components/NotificationsV2/NotificationsCard/NotificationsCard.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import { useRouter } from "vue-router";
import { RouteName } from "@/constants/route_name";

const props = defineProps<{
  data: TBonusBalanceEventData;
  createdAt: string;
  readAt: string | null;
}>();

const router = useRouter();

const handleUseBonusClick = () => {
  router.push({ name: RouteName.CREATE_CARD });
};
</script>

<template>
  <NotificationsCard
    :label="$t('notifications.promo-5-usd-bonus-balance.title')"
    :date="props.createdAt"
    :unread="!props.readAt"
    :hoverable="false">
    <template #description>
      <div class="flex flex-col gap-2.5 items-start">
        <UI2Text class="text-fg-base-tertiary">
          {{ $t("notifications.promo-5-usd-bonus-balance.text") }}
        </UI2Text>

        <UI2Button
          size="s"
          variant="secondary"
          color="accent"
          @click="handleUseBonusClick">
          {{ $t("notifications.promo-5-usd-bonus-balance.use-it-btn") }}
        </UI2Button>
      </div>
    </template>
  </NotificationsCard>
</template>
