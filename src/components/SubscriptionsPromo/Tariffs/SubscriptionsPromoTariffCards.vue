<script lang="ts" setup>
// Core
import { computed, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Skeletor } from "vue-skeletor";
import { omit } from "lodash";

// Types
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import type { TNavigationKey } from "@/components/SubscriptionsPromo/types";
import type { TSubscriptionResource } from "@/types/api/TSubscriptionResource";

// Composables & helpers & constants & services
import { useAutoAnimate } from "@formkit/auto-animate/vue";
import { EmitEventsNames } from "@/constants/emit_events_names";
import eventEmitter from "@/services/EventEmitter";
import { RouteName } from "@/constants/route_name";
import { useSubscriptionsGet } from "@/composable/API/useSubscriptionsGet";
import { useCountrySets } from "@/composable/useCountrySets";
import { scrollToSmooth } from "@/helpers/smoothScrollTo";
import { SUBSCRIPTION_SCROLLTO_OFFSET } from "@/constants/subscription_scrollto_offset";

// Components
import SubscriptionsPromoTariffCard from "@/components/SubscriptionsPromo/Tariffs/SubscriptionsPromoTariffCard.vue";
import SubscriptionBuyModal from "@/components/Subscriptions/SubscriptionBuyModal.vue";
import SubscriptionPromoBuyModal from "@/components/Subscriptions/SubscriptionPromoBuyModal.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
/* eslint-disable max-len */
import NotificationModalBeforeDowngrade from "@/components/SubscriptionDetail/Tariffs/NotificationModalBeforeDowngrade.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import SubscriptionSuccessPurchaseModal from "@/components/Subscriptions/SubscriptionsModalsFlow/ui/SubscriptionSuccessPurchaseModal.vue";

const [tariffsContainer] = useAutoAnimate();

const { countrySetGuard } = useCountrySets();
const props = defineProps<{
  tariffs: TSubscriptionTariffResource[];
  currentMainTariff?: TSubscriptionResource;
  isHaveSubscription: boolean;
  isActiveExperiment1: false | number | null;
  isActiveExperiment2: false | number | null;
  isActiveExperimentRetention?: 0 | 1;
  isLoading: boolean;
}>();

const router = useRouter();
const route = useRoute();
const isShowModalHaveActiveCards = ref(false);
const selectedNewTariff = ref<TSubscriptionTariffResource | null>(null);
const isShowBuyModal = ref(false);
const isShowPromoBuyModal = ref(false);
const isSuccessBuySubscriptionModal = ref(false);
const selectTariff = ref<TSubscriptionTariffResource | null>(null);
const newTariffCardsLimitWithExtensions = ref<number>(0);
const currentTariffActiveCardsCount = ref<number | null>(0);

// Shows BuyModal - depends on retemptionUsers experiment
const showBuyModal = () => {
  if (props.isActiveExperimentRetention) {
    isShowPromoBuyModal.value = true;
  } else {
    isShowBuyModal.value = true;
  }
};

// Hides BuyModal - depends on retemptionUsers experiment
const hideBuyModal = () => {
  if (props.isActiveExperimentRetention) {
    isShowPromoBuyModal.value = false;
  } else {
    isShowBuyModal.value = false;
  }
};

watch([() => props.tariffs.length, () => props.isLoading], (value) => {
  if (route.query.name && value[0] > 0 && !value[1]) {
    selectTariff.value =
      props.tariffs.find((item) => item.slug === route.query.name) ?? null;

    if (!selectTariff.value) {
      return;
    }

    if (!props.isHaveSubscription) {
      showBuyModal();
      return;
    }

    isUpgradeTariff(selectTariff.value.slug)
      ? upgradeTariff(selectTariff.value)
      : downgradeTariff(selectTariff.value);
  }
});

const calcTotalExtensionLimitCards = (
  subscriptions: TSubscriptionResource[]
) => {
  let total = 0;
  for (const subscription of subscriptions) {
    if (subscription?.status_name !== "ACTIVE") continue;
    if (subscription?.subscription_tariff?.type_name !== "EXTENSION") continue;
    total += subscription.subscription_tariff?.cards_limit || 0;
  }
  return total;
};

const calcCurrentTariffActiveCards = (
  subscriptions: TSubscriptionResource[]
) => {
  for (const subscription of subscriptions) {
    if (subscription?.status_name !== "ACTIVE") continue;
    if (subscription?.subscription_tariff?.type_name === "MAIN") {
      return subscription?.cards_count - subscription?.cards_blocked_count;
    }
  }
  return null;
};

const upgradeTariff = (tariff: TSubscriptionTariffResource) => {
  eventEmitter.emit(EmitEventsNames.MODALS_TARIFF_INCREASE, {
    currentTariff: props.currentMainTariff?.subscription_tariff,
    newTariff: tariff,
    isUpgrade: true,
  });
};

const downgradeTariff = async (tariff: TSubscriptionTariffResource) => {
  if (tariff?.cards_limit === undefined) return;

  const newTariffCardsLimit = tariff?.cards_limit;

  const { data } = await useSubscriptionsGet();

  const extensionAmount = data.value?.data?.reduce((acc, item) => {
    if (item.subscription_tariff.type_name === "EXTENSION") {
      acc = acc + Number(item.subscription_tariff.amount);
    }

    return acc;
  }, 0);

  const totalCardsLimitExtensions = calcTotalExtensionLimitCards(
    data.value?.data || []
  );
  currentTariffActiveCardsCount.value = calcCurrentTariffActiveCards(
    data.value?.data || []
  );

  if (currentTariffActiveCardsCount.value === null) return;

  newTariffCardsLimitWithExtensions.value =
    newTariffCardsLimit + totalCardsLimitExtensions;

  if (
    currentTariffActiveCardsCount.value >
    newTariffCardsLimitWithExtensions.value
  ) {
    selectedNewTariff.value = tariff;
    isShowModalHaveActiveCards.value = true;
  } else {
    eventEmitter.emit(EmitEventsNames.MODALS_TARIFF_INCREASE, {
      currentTariff: props.currentMainTariff?.subscription_tariff,
      newTariff: tariff,
      extentionsAmount: extensionAmount,
      isUpgrade: false,
    });
  }
};

const selectTariffHandler = async (tariff: TSubscriptionTariffResource) => {
  countrySetGuard(
    () => {
      router.push({ query: { ...route.query, name: tariff.slug } });

      if (!props.isHaveSubscription) {
        selectTariff.value = tariff;
        showBuyModal();

        return;
      }

      isUpgradeTariff(tariff?.slug)
        ? upgradeTariff(tariff)
        : downgradeTariff(tariff);
    },
    () => {},
    () => {},
    true
  );
};

const onSeeCards = () => {
  const cardsSectionId: TNavigationKey = "cards";
  const el = document.getElementById(cardsSectionId);
  if (el) {
    scrollToSmooth(el.offsetTop - SUBSCRIPTION_SCROLLTO_OFFSET);
  }
};

const isUpgradeTariff = (newTariffSlug: string): boolean => {
  if (!currentTariffSlug.value || !newTariffSlug) return false;

  const normalizeSlug = (slug: string) => {
    return slug.replace(/_.+$/, "").toLowerCase();
  };

  const weights: { [key: string]: number } = {
    "extra small": 0,
    small: 1,
    medium: 2,
    large: 3,
  };

  return (
    weights[normalizeSlug(newTariffSlug)] >=
    weights[normalizeSlug(currentTariffSlug.value)]
  );
};

const successBuySubscription = () => {
  hideBuyModal();
  isSuccessBuySubscriptionModal.value = true;
};

const currentTariffSlug = computed(() => {
  return props.currentMainTariff?.subscription_tariff?.slug;
});

const redirectToCards = () => {
  router.push({ name: RouteName.CARDS });
};

const hideModalActiveCards = () => {
  router.replace({ query: omit(route.query, ["name"]) });
  isShowModalHaveActiveCards.value = false;
};

const hideSubscriptionBuyModal = () => {
  router.replace({ query: omit(route.query, ["name"]) });
  hideBuyModal();
};
</script>

<template>
  <div class="flex flex-col w-full">
    <div
      v-if="props.isLoading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
      <Skeletor
        as="div"
        class="rounded"
        height="25.375rem" />
      <Skeletor
        as="div"
        class="rounded"
        height="25.375rem" />
      <Skeletor
        as="div"
        class="rounded"
        height="25.375rem" />
      <Skeletor
        as="div"
        class="rounded col-span-1 lg:col-span-3 xl:col-span-1"
        height="25.375rem" />
    </div>
    <div
      v-else
      ref="tariffsContainer"
      class="tariffs-wrapper">
      <div
        v-if="props.tariffs.length"
        class="tariffs">
        <SubscriptionsPromoTariffCard
          v-for="tariff of props.tariffs"
          :key="tariff.id"
          :is-active-experiment1="isActiveExperiment1"
          :is-active-experiment2="isActiveExperiment2"
          :is-active-experiment-retention="isActiveExperimentRetention"
          :is-current-tariff="tariff.slug === currentTariffSlug"
          :is-have-subscription="isHaveSubscription"
          :tariff="tariff"
          @select-tariff="selectTariffHandler"
          @see-cards="onSeeCards" />
      </div>

      <Loader v-else />
    </div>

    <NotificationModalBeforeDowngrade
      v-if="props.currentMainTariff?.subscription_tariff && selectedNewTariff"
      :close="hideModalActiveCards"
      :confirm-button-text="
        $t('pst-private.modal.have-active-cards.button.open')
      "
      :current-tariff="props.currentMainTariff?.subscription_tariff as TSubscriptionTariffResource"
      :is-open="isShowModalHaveActiveCards"
      :new-tariff="selectedNewTariff as TSubscriptionTariffResource"
      :success="redirectToCards"
      :text="
        $t('pst-private.modal.downgrade.have-active-cards.text', {
          a: selectedNewTariff?.name,
          b: newTariffCardsLimitWithExtensions || 0,
        })
      "
      :title="$t('pst-private.modal.have-active-cards.title')">
      <template #content>
        <div
          class="flex flex-none flex-col w-full gap-2 bg-bg-level-1 rounded p-4">
          <div class="flex flex-none w-full flex-row">
            <div class="flex flex-auto text-fg-secondary text-4 leading-5">
              {{
                $t("pst-private.modal.have-active-cards.table.available-cards")
              }}
              {{ selectedNewTariff?.name }}
            </div>
            <div class="flex flex-none text-fg-primary text-4 leading-5">
              {{ newTariffCardsLimitWithExtensions }}
            </div>
          </div>
          <div class="flex flex-none w-full flex-row">
            <div class="flex flex-auto text-fg-secondary text-4 leading-5">
              {{ $t("pst-private.modal.have-active-cards.table.active-cards") }}
              {{ props.currentMainTariff?.subscription_tariff?.name }}
            </div>
            <div class="flex flex-none text-fg-primary text-4 leading-5">
              {{ currentTariffActiveCardsCount }}
            </div>
          </div>
          <div class="flex w-full">
            <DynamicIcon
              class="w-full my-2"
              name="dots-new-card" />
          </div>
          <div class="flex flex-none w-full flex-row">
            <div
              class="flex flex-auto text-fg-primary font-semibold text-4 leading-5">
              {{
                $t(
                  "pst-private.modal.have-active-cards.table.cards-to-turn-off-left"
                )
              }}
            </div>
            <div
              class="flex flex-none text-fg-primary font-semibold text-4 leading-5">
              {{
                (currentTariffActiveCardsCount || 0) -
                (newTariffCardsLimitWithExtensions || 0)
              }}
            </div>
          </div>
        </div>
      </template>
    </NotificationModalBeforeDowngrade>

    <SubscriptionBuyModal
      v-if="isShowBuyModal"
      :select-tariff="selectTariff!"
      @close="hideSubscriptionBuyModal"
      @success-buy-subscription="successBuySubscription" />

    <SubscriptionPromoBuyModal
      v-if="isShowPromoBuyModal"
      :select-tariff="selectTariff!"
      @close="hideSubscriptionBuyModal"
      @success-buy-subscription="successBuySubscription" />

    <SubscriptionSuccessPurchaseModal
      :content-text="$t('pst-private.subscriptions.page.extraSmall.subtitle')"
      :content-title="$t('pst-private.subscriptions.page.extraSmall.title')"
      :modal-title="$t('Purchase completed')"
      :open="isSuccessBuySubscriptionModal"
      :tariff="selectTariff" />
  </div>
</template>

<style lang="scss" scoped>
.tariffs-wrapper {
  @apply w-full;
}

.tariffs {
  @apply flex justify-center gap-2 flex-wrap lg:flex-nowrap
  max-w-[460px] lg:max-w-full mx-auto;
}
</style>
