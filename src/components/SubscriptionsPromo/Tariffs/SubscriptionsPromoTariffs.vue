<script setup lang="ts">
// Core
import { computed } from "vue";

// Composables & helpers & constants
import { useSubscriptionsInfoGet, useSubscriptionsList } from "@/composable";
import { useUserSubscriptionsTariffs } from "@/composable/useUserSubscriptionsTariffs";
import { SubscriptionStatusCode } from "@/constants/subscription_status_code";

// Components
import SubscriptionsPromoSectionHead from "@/components/SubscriptionsPromo/SubscriptionsPromoSectionHead.vue";
import SubscriptionsPromoTariffCards from "@/components/SubscriptionsPromo/Tariffs/SubscriptionsPromoTariffCards.vue";

const { data: subscriptionsInfo, isFetching: isFetchingSubscriptionInfo } =
  useSubscriptionsInfoGet();

const { currentMainTariff, isFetching: isFetchingCurrentMainTariff } =
  useSubscriptionsList();

const {
  userSubscriptionsTariffs,
  isActiveNewSubscriptionsExperiment1,
  isActiveNewSubscriptionsExperimentTwo,
  isActiveSubscriptionsExperimentRetentionUsers,
  isFetching: isFetchingTariffs,
} = useUserSubscriptionsTariffs();

const isHaveSubscription = computed(() => {
  return (
    subscriptionsInfo.value?.data?.status === SubscriptionStatusCode.ACTIVE
  );
});

const isLoadingData = computed(() => {
  return (
    isFetchingTariffs.value ||
    isFetchingCurrentMainTariff.value ||
    isFetchingSubscriptionInfo.value
  );
});
</script>

<template>
  <div class="tariffs">
    <SubscriptionsPromoSectionHead
      class="mb-10 items-center text-center"
      :title="$t('subscription-promo.monthly-plans')"
      :subtitle="$t('subscription-promo.plans-description')"
      :tag-text="$t('subscription-promo.pricing-n-terms')" />

    <SubscriptionsPromoTariffCards
      :current-main-tariff="currentMainTariff"
      :is-active-experiment1="isActiveNewSubscriptionsExperiment1"
      :is-active-experiment2="isActiveNewSubscriptionsExperimentTwo"
      :is-active-experiment-retention="
        isActiveSubscriptionsExperimentRetentionUsers
      "
      :is-have-subscription="isHaveSubscription"
      :is-loading="isLoadingData"
      :tariffs="userSubscriptionsTariffs" />
    <div class="text-center py-2.5 text-fg-secondary">
      {{ $t("subscriptions.page.allCardsExcept") }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tariffs {
  @apply relative;
}
</style>
