<script lang="ts" setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import { SubscriptionRetentionUsersExperiment } from "@/constants/subscription_retention_users_experiment";
import UIButton from "@/components/ui/UIButton/UIButton.vue";

const props = defineProps<{
  isHaveSubscription: boolean;
  isActiveExperiment1: false | number | null;
  isActiveExperiment2: false | number | null;
  isActiveExperimentRetention?: 0 | 1;
  tariff: TSubscriptionTariffResource;
  isCurrentTariff: boolean;
  hideSeeCards?: boolean;
}>();

const emit = defineEmits<{
  selectTariff: [tariff: TSubscriptionTariffResource];
  seeCards: [];
}>();

const { t } = useI18n();

const selectTariffHandler = (tariff: TSubscriptionTariffResource) => {
  emit("selectTariff", tariff);
};

const onSeeCards = () => {
  emit("seeCards");
};

const isShowDiscount = computed(() => {
  return !["Extra Small"].includes(props.tariff.name) && discountDisplay.value;
});

const discountDisplay = computed<boolean>(() => {
  return !(
    props.isActiveExperiment1 === 1 ||
    props.isActiveExperiment2 === 1 ||
    props.isActiveExperiment2 === 0 ||
    (props.isActiveExperiment2 === false && props.isActiveExperiment1 === false)
  );
});

const btnTrackConfig = computed(() => {
  return {
    page: "/app/subscription/view-tariffs",
    page_version: 0,
    name: `Get ${props.tariff.cards_limit} cards`,
    version: 0,
  };
});

const btnTariffCardText = computed(() => {
  if (props.isHaveSubscription) {
    if (props.isCurrentTariff) {
      return t("subscriptions.page.currentTariff");
    } else {
      return `${t("subscriptions.page.switchToTariff")} ${props.tariff.name}`;
    }
  }
  if (!discountDisplay.value) {
    if (props.isActiveExperimentRetention) {
      // For Retention Users experiment display single word "Select"
      return t("select");
    } else {
      return `${t("select")} ${props.tariff.name}`;
    }
  }
  return `${t("subscriptions.page.firstMonthFor")} ${Number(
    props.tariff.amount_first
  )} $`;
});

const tariffDiscount = computed(() => {
  if (!isShowDiscount.value) return 0;
  return parseInt(props.tariff.amount) - parseInt(props.tariff.amount_first);
});

/**
 * Method returns tariff amount with discount for RetentionUsers exepriment
 * @param {number} amount - Tariff amount
 */
const retentionUsersTariffAmount = (amount: number) => {
  if (!props.isActiveExperimentRetention) {
    return Math.trunc(amount);
  }

  const amountRes =
    amount -
    (amount / 100) * SubscriptionRetentionUsersExperiment.DISCOUNT_PERCENT;
  // Round amount if it is not integer
  return Number.isInteger(amountRes) ? amountRes : amountRes.toFixed(2);
};
</script>

<template>
  <div
    class="card"
    data-testid="subscriptions-tariff-card">
    <div class="card__head">
      <!-- Tariff Name & Price -->
      <div
        class="flex gap-1 text-5 leading-6"
        :class="{ 'flex-col': isActiveExperimentRetention }">
        <div
          class="grow font-medium -tracking-[0.6px]"
          data-testid="tariff-name">
          {{ props.tariff.name }}
        </div>
        <div v-if="isActiveExperimentRetention">
          <div class="flex gap-2.5">
            <div class="card__current-amount">
              ${{ retentionUsersTariffAmount(Number(props.tariff.amount)) }}
            </div>
            <div class="card__original-amount">
              ${{ Number(props.tariff.amount).toFixed(0) }}
            </div>
          </div>
        </div>
        <div
          v-else
          class="shrink-0 text-fg-secondary">
          ${{ Number(props.tariff.amount).toFixed(0) }}
        </div>
      </div>
      <!-- Cards Limit -->
      <div class="flex items-center gap-2.5 mt-4">
        <div class="card__limit">
          {{ props.tariff.cards_limit }}
        </div>
        <div class="card__limit-label">
          <div>
            {{
              $t(
                "subscription-promo.card-pluralization",
                props.tariff.cards_limit
              )
            }}
          </div>
          <div>
            {{ $t("subscriptions.page.perMonth") }}
          </div>
        </div>
      </div>
    </div>

    <div class="card__body">
      <div class="tariff-param">
        <div class="tariff-param__value">
          {{ Number(props.tariff.fee_topup).toFixed(0) }}%
        </div>
        <div class="tariff-param__label">
          {{ $t("subscription-promo.deposit-fee") }}
        </div>
      </div>
      <div class="tariff-param">
        <div class="tariff-param__value">
          {{ parseInt(props.tariff.cashback_percent) }}%
        </div>
        <div class="tariff-param__label">
          {{ $t("Cashback") }}
        </div>
      </div>
      <div class="tariff-param">
        <div class="tariff-param__value">
          ${{ $n(parseInt(props.tariff.cashback_limit)) }}
        </div>
        <div class="tariff-param__label">
          {{ $t("subscription-promo.max-cashback") }}
        </div>
      </div>

      <div class="flex flex-col w-full">
        <UIButton
          v-track:button="btnTrackConfig"
          data-testid="select-tariff-button"
          :color="isActiveExperimentRetention ? 'orange-solid' : 'black'"
          :disabled="props.isHaveSubscription && isCurrentTariff"
          @click="selectTariffHandler(props.tariff)">
          {{ btnTariffCardText }}
        </UIButton>

        <UIButton
          v-if="!hideSeeCards"
          class="mt-2"
          @click="onSeeCards">
          {{ $t("subscription-promo.see-cards") }}
        </UIButton>

        <div
          v-if="isShowDiscount"
          class="text-4 leading-5 flex justify-center mt-2 text-fg-secondary">
          {{ $t("subscriptions.page.discount") }} -{{ tariffDiscount }} $
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.card {
  @apply flex flex-col flex-[1_1_45%] overflow-hidden
  rounded border border-solid border-bg-level-1-5;

  &__head {
    @apply p-2 md:p-4 bg-bg-level-0;
  }

  &__body {
    @apply flex flex-col gap-7 grow p-2 md:p-4 bg-bg-level-1;
  }

  &__current-amount {
    @apply text-fg-orange;
  }

  &__original-amount {
    @apply relative text-fg-secondary;

    &:before {
      @apply content-[''] block absolute top-[50%] left-0 w-full h-0.5 bg-fg-secondary;
    }
  }

  &__limit {
    @apply text-10 leading-11 font-black -tracking-[1.2px];
    background: linear-gradient(
      0deg,
      rgba(21, 24, 30, 0.48) 0%,
      rgb(21, 24, 30) 100%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  &__limit-label {
    @apply text-4 leading-[18px] text-fg-secondary -tracking-[0.48px] whitespace-nowrap;
  }
}

.tariff-param {
  &__value {
    @apply text-5 leading-6 font-medium;
  }

  &__label {
    @apply text-4 leading-5 text-fg-secondary lowercase;
  }
}
</style>
