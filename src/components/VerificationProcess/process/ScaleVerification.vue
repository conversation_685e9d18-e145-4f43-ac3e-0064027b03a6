<script setup lang="ts">
import { onMounted, ref } from "vue";
import {
  TOAST_TYPE,
  useCallToast,
  useVerificationActualGet,
  useVerificationPost,
} from "@/composable";
import { useTimeoutPoll } from "@vueuse/core";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";

const { t } = useI18n();
const { getUser: updateUserStore } = useUserStore();

const props = defineProps<{
  selectedCountryId?: number;
}>();

const emit = defineEmits<{
  success: [value: boolean];
}>();

const isSending = ref<boolean>(false);
const iframeUrl = ref<string | null>(null);

const checkIfVerificationIsDone = async () => {
  const { data } = await useVerificationActualGet();

  const step = data.value?.data?.step?.toLowerCase();

  if (step === "scale") {
    await updateUserStore();
    emit("success", true);
  }
};

const { resume } = useTimeoutPoll(checkIfVerificationIsDone, 5000, {
  immediate: false,
});

const onSendVerificationRequest = async () => {
  isSending.value = true;

  const { data } = await useVerificationPost({
    country_id: props.selectedCountryId,
  });

  if (data.value?.success) {
    const url = data.value?.url;
    if (url) {
      iframeUrl.value = url;
      resume();
    }
  } else {
    useCallToast({
      title: data.value?.message ?? t("errors.universal-request-error"),
      options: {
        type: TOAST_TYPE.ERROR,
        id: "verification-post-error",
      },
    });
  }

  isSending.value = false;
};
onMounted(() => {
  onSendVerificationRequest();
});
</script>

<template>
  <div>
    <div v-if="iframeUrl">
      <iframe
        id="sumframe"
        :src="iframeUrl"
        allow="camera; microphone"
        class="frame" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.frame {
  @apply w-full border-none;
  height: calc(100vh - 185px);
}
</style>
