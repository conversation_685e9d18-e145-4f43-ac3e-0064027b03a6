<script setup lang="ts">
import { useVerificationActualGet, useVerificationPost } from "@/composable";
import { useTimeoutPoll } from "@vueuse/core";
import Loader from "@/components/ui/Loader/Loader.vue";

const props = defineProps<{
  selectedCountryId?: number;
}>();

const emit = defineEmits<{
  close: [];
}>();

const { data: verificationPostData, isFetching } = useVerificationPost(
  {
    step: "unlimited",
    country_id: props.selectedCountryId,
  },
  { immediate: true }
);

const checkIfVerificationIsDone = async () => {
  const { data } = await useVerificationActualGet();

  const step = data.value?.data?.step?.toLowerCase();

  if (step === "unlimited" || step === "corporate") {
    emit("close");
  }
};

useTimeoutPoll(checkIfVerificationIsDone, 5000, {
  immediate: true,
});
</script>

<template>
  <div class="flex-col flex items-center">
    <div class="w-full">
      <Loader v-if="isFetching" />
      <iframe
        v-else-if="verificationPostData?.url"
        id="sumframe"
        :src="verificationPostData.url"
        allow="camera; microphone"
        class="frame" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.frame {
  @apply min-w-full border-none;
  height: calc(100vh - 185px);
}
</style>
