<script setup lang="ts">
import { computed, ref } from "vue";
import { useVerificationState } from "@/composable";
import type { TVerificationTier } from "@/types/verification/verification";
import Loader from "@/components/ui/Loader/Loader.vue";
import UITransition from "@/components/ui/UITransition.vue";
import ScaleVerification from "@/components/VerificationProcess/process/ScaleVerification.vue";
import UnlimitedVerification from "@/components/VerificationProcess/process/UnlimitedVerification.vue";
import WelcomeVerificationV2 from "@/components/WelcomeVerificationV2/WelcomeVerificationV2.vue";
import VerificationSelectCountry from "@/components/VerificationProcess/VerificationSelectCountry.vue";
import VerificationManagerHelpScreen from "@/components/VerificationProcess/VerificationManagerHelpScreen.vue";

const { userVerificationTier, nextVerificationData, isLoading } =
  useVerificationState();

const props = withDefaults(
  defineProps<{
    isCustomCase?: boolean;
  }>(),
  {
    isCustomCase: false,
  }
);

const emit = defineEmits<{
  success: [value: boolean];
}>();

const isCountryVerified = ref<boolean>(false);
const selectedCountryId = ref<number>();
const isManagerHelpSkipped = ref<boolean>(false);

const nextVerificationTier = computed<TVerificationTier>(() => {
  return nextVerificationData.value?.slug ?? null;
});

/**
 * Hide Selection country:
 * - in getting `welcome` tier 'cause Welcome Form allready has it
 * - in getting `unlimited` tier when user already has `scale` tier
 */
const showCountrySelectScreen = computed(() => {
  const isGettingWelcomeTier = nextVerificationTier.value === "welcome";
  const isGettingUnlimitedWithScale =
    nextVerificationTier.value === "unlimited" &&
    userVerificationTier.value === "scale";

  return (
    !isCountryVerified.value &&
    !isGettingWelcomeTier &&
    !isGettingUnlimitedWithScale
  );
});

const isManagerHelpScreen = computed(() => {
  return (
    !isManagerHelpSkipped.value &&
    props.isCustomCase &&
    nextVerificationTier.value !== "welcome"
  );
});

const handleSelectCountrySuccess = (countryId: number) => {
  selectedCountryId.value = countryId;
  isCountryVerified.value = true;
};

const onSuccessHandle = () => {
  emit("success", true);
};
</script>

<template>
  <div
    v-if="isLoading"
    class="fixed inset-0 flex items-center justify-center">
    <Loader />
  </div>
  <div v-else>
    <UITransition name="fade-slide-down">
      <VerificationSelectCountry
        v-if="showCountrySelectScreen"
        @success="handleSelectCountrySuccess" />

      <VerificationManagerHelpScreen
        v-else-if="isManagerHelpScreen"
        @continue="isManagerHelpSkipped = true" />

      <template v-else>
        <WelcomeVerificationV2
          v-if="nextVerificationTier === 'welcome'"
          class="max-w-[33.75rem] md:px-5 w-full mx-auto"
          @success="onSuccessHandle" />

        <ScaleVerification
          v-else-if="nextVerificationTier === 'scale'"
          :selected-country-id="selectedCountryId"
          @success="onSuccessHandle" />

        <UnlimitedVerification
          v-else-if="nextVerificationTier === 'unlimited'"
          :selected-country-id="selectedCountryId"
          @close="onSuccessHandle" />
      </template>
    </UITransition>
  </div>
</template>
