<script lang="ts" setup>
import { computed, ref } from "vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import CreateCardSelectType from "@/components/CreateCardSelectType/CreateCardSelectType.vue";
import CreateCardSelectBin from "@/components/CreateCardSelectBin/CreateCardSelectBin.vue";
import CreateCardApprove from "@/components/CreateCardApprove/CreateCardApprove.vue";
import CreateCardSuccess from "@/components/CreateCardSuccess/CreateCardSuccess.vue";
import UITransition from "@/components/ui/UITransition.vue";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import {
  CardCreateMachineState,
  useCreateCardMachine,
} from "@/components/CreateCardV2/useCreateCardMachine";
import Loader from "@/components/ui/Loader/Loader.vue";
import eventEmitter from "@/services/EventEmitter";
import { EmitEventsNames } from "@/constants/emit_events_names";
import { TARIFF_SLUGS_NAMES_MAP } from "@/constants/tariff_slugs_names_map";
import type { TCardType } from "@/components/CreateCardV2/types";
import {
  TrackerEvent,
  useCardAutoRefillPost,
  useCountrySets,
  useSubscriptionsInfo,
  useTracker,
  useVerificationState,
} from "@/composable";
import { useCardsGet } from "@/composable/API/useCardsGet";
// import SecurityWarningVerification from "../SecurityWarning/SecurityWarningVerification.vue";
import VerificationProcess from "@/components/VerificationProcess/VerificationProcess.vue";
import { useUserStore } from "@/stores/user";
import { useGlobalUniversalDialog } from "../GlobalUniversalDialog/useGlobalUniversalDialog";
import type { TCardAutoRefillResource } from "@/types/api/TCardAutoRefillResource";
import type { TCardResource } from "@/types/api/TCardResource";
import CreateCardUltimaIssue from "@/components/CreateCardUltimaIssue/CreateCardUltimaIssue.vue";
import CreateCardUltimaIssueV2 from "@/components/CreateCardUltimaIssueV2/CreateCardUltimaIssueV2.vue";
import CreateCardApproveV2 from "@/components/CreateCardApproveV2/CreateCardApproveV2.vue";
import CreateCardSuccessV2 from "@/components/CreateCardSuccessV2.vue";
import CreateCardAutoBuyPayment from "@/components/CreateCardAutoBuyPayment/CreateCardAutoBuyPayment.vue";
import CreateCardAutoBuyPaymentV2 from "@/components/CreateCardAutoBuyPaymentV2/CreateCardAutoBuyPaymentV2.vue";
import CreateCardIssue from "@/components/CreateCardIssue/CreateCardIssue.vue";
import CreateCardSelectTariff from "@/components/CreateCardSelectTariff/CreateCardSelectTariff.vue";
import { LocalStorageKey } from "@/constants/local_storage_key";

const { t } = useI18n();
const router = useRouter();
const tracker = useTracker();
const { subscriptionsStatus } = useSubscriptionsInfo();
const { isTeamMember } = useUserStore();
const { send, step, context, snapshot } = useCreateCardMachine();
const { openDialog, closeDialog } = useGlobalUniversalDialog();
const { isActive: haveNeedVerificationAction } = useCountrySets();

const isOpen = ref<boolean>(true);

const modalSubtitle = computed<string | null>(() => {
  if (!context.value.cardTariff) {
    return t("create-card.modal-no-tariff-subtitle");
  }

  return TARIFF_SLUGS_NAMES_MAP[context.value.cardTariff];
});

const canGoBack = computed(() => {
  return snapshot.value.can({
    type: "BACK",
  });
});

const closeHandle = () => {
  send({ type: "RESET" });
  router.push({ name: RouteName.DASHBOARD });
};

const selectCardType = (cardType: TCardType) => {
  if (!subscriptionsStatus.value && isTeamMember && cardType === "forAdv") {
    onOpenRequestPrivateFromMasterDialog();
  } else if (
    !subscriptionsStatus.value &&
    cardType === "forAdv" &&
    context.value.isAdvWithSubActive &&
    context.value.isAutoBuy
  ) {
    send({ type: "SELECT_CARD_TYPE", value: cardType });
  } else if (cardType === "forAdv") {
    router.push({
      name: RouteName.SUBSCRIPTION_TARIFF,
    });
  } else {
    send({ type: "SELECT_CARD_TYPE", value: cardType });
  }
};

const onOpenRequestPrivateFromMasterDialog = () => {
  openDialog({
    title: t("pst-private.required-private-dialog-title"),
    text: t("pst-private.required-private-dialog-text"),
    showBtnConfirm: true,
    btnConfirmText: t("OK"),
    showBtnCancel: false,
    withCloseIcon: true,
    callbackConfirm: closeDialog,
  });
};

const autoRefillInfo = ref<TCardAutoRefillResource | null>(null);

const enableAutoRefill = async (resultCard: TCardResource) => {
  const { data: autoRefillData } = await useCardAutoRefillPost({
    card_id: resultCard.id,
    minimum_balance: "50",
    amount_refill: "50",
  });
  if (autoRefillData.value?.data) {
    autoRefillInfo.value = autoRefillData.value.data;
  }
};

const logBuyCardEvent = () => {
  const isNewLandingExperiment = localStorage.getItem(
    LocalStorageKey.NEW_PST_LANDING_EXPERIMENT
  );

  if (isNewLandingExperiment) {
    tracker.logEvent(TrackerEvent.BUY_CARD_NEWPST);
  }
};

const handleSingleBuySuccess = async (resultCard: TCardResource) => {
  await enableAutoRefill(resultCard);
  send({ type: "SINGLE_BUY_SUCCESS", value: resultCard });
  logBuyCardEvent();
};

const handleMultiBuySuccess = () => {
  send({ type: "MULTI_BUY_SUCCESS" });
  logBuyCardEvent();
};

const handleAutoBuySuccess = async () => {
  const { data: cards } = await useCardsGet();
  const resultCard = cards.value?.data?.length ? cards.value.data[0] : null;

  if (resultCard) {
    await enableAutoRefill(resultCard);
  }

  send({ type: "AUTO_BUY_SUCCESS", value: resultCard });
  logBuyCardEvent();
};

const confirmHandle = () => {
  isOpen.value = false;

  if (context.value.resultCard) {
    eventEmitter.emit(EmitEventsNames.MODALS_CARD_SETTING, {
      cardDetail: {
        ...context.value.resultCard,
        auto_refill: {
          id: autoRefillInfo.value?.id,
          active: true,
          minimum_balance: autoRefillInfo.value?.minimum_balance,
          amount_refill: autoRefillInfo.value?.amount_refill,
        },
      },
      onClose: closeHandle,
    });
  } else {
    closeHandle();
  }
};
</script>

<template>
  <UIFullScreenModal
    :is-open="isOpen"
    :title="t('create-card.modal-title')"
    :subtitle="modalSubtitle"
    :can-go-back="canGoBack"
    :step="context.step"
    :num-steps="context.numSteps"
    @close="closeHandle"
    @prev-step="send({ type: 'BACK' })">
    <template #content>
      <div class="pt-0 max-w-[73.75rem] mx-auto">
        <UITransition :name="'fade-slide-down'">
          <div v-if="step === CardCreateMachineState.INITIALIZATION">
            <Loader />
          </div>
          <div v-if="step === CardCreateMachineState.SELECT_CARD_TYPE_STEP">
            <CreateCardSelectType @select-card-type="selectCardType" />
          </div>
          <div v-if="step === CardCreateMachineState.VERIFICATION_STEP">
            <VerificationProcess
              :is-custom-case="
                context.cardType === 'forAdv' && haveNeedVerificationAction
              "
              @success="send({ type: 'VERIFICATION_COMPLETED' })" />
          </div>
          <div v-if="step === CardCreateMachineState.SELECT_CARD_TARIFF_STEP">
            <CreateCardSelectTariff
              :issue-mode="true"
              :show-ultima="context.cardType === null"
              class="pb-10"
              @select-card-tariff="
                (tariff) => send({ type: 'SELECT_CARD_TARIFF', value: tariff })
              " />
          </div>
          <div
            v-if="
              step === CardCreateMachineState.SELECT_BIN_STEP &&
              context.cardTariff
            ">
            <CreateCardSelectBin
              :slug="context.cardTariff"
              :is-adv-with-sub-active="context.isAdvWithSubActive"
              :is-autobuy="context.isAutoBuy"
              @select-bin="
                (bin) => send({ type: 'SELECT_CARD_BIN', value: bin })
              " />
          </div>
          <div
            v-if="
              step ===
                CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP_EXPERIMENT &&
              context.cardTariff
            ">
            <CreateCardUltimaIssueV2
              :is-auto-buy="context.isAutoBuy"
              @submit-form="
                (cardForIssue, promoCode) => {
                  send({
                    type: 'SET_PROMO_CODE',
                    value: promoCode,
                  });
                  send({
                    type: 'ULTIMA_CARD_ISSUE',
                    value: cardForIssue,
                  });
                }
              " />
          </div>

          <div
            v-if="
              step === CardCreateMachineState.APPROVE_CARD_STEP_EXPERIMENT &&
              context.cardForIssue
            ">
            <CreateCardApproveV2
              :card-for-issue="context.cardForIssue!"
              :promo-code-data="context.promoCodeData"
              @single-buy-success="handleSingleBuySuccess"
              @multi-buy-success="handleMultiBuySuccess" />
          </div>

          <div
            v-if="
              // prettier-ignore
              step === CardCreateMachineState.ULTIMA_CARD_ISSUE_STEP &&
            context.cardTariff
            ">
            <CreateCardUltimaIssue
              :card-tariff-slug="context.cardTariff"
              :is-auto-buy="context.isAutoBuy"
              @select-card-tariff="
                (tariff) => send({ type: 'SELECT_CARD_TARIFF', value: tariff })
              "
              @set-promo-code="
                (promoCodeData) =>
                  send({ type: 'SET_PROMO_CODE', value: promoCodeData ?? null })
              "
              @issue-card="
                (cardForIssue) =>
                  send({
                    type: 'ULTIMA_CARD_ISSUE',
                    value: cardForIssue,
                  })
              " />
          </div>
          <div
            v-if="
              step === CardCreateMachineState.DEFAULT_CARD_ISSUE_STEP &&
              context.cardTariff &&
              context.selectedBin
            ">
            <CreateCardIssue
              :bin="context.selectedBin"
              :card-tariff-slug="context.cardTariff"
              :is-auto-buy="context.isAutoBuy"
              @set-promo-code="
                (promoCodeData) =>
                  send({ type: 'SET_PROMO_CODE', value: promoCodeData ?? null })
              "
              @issue-card="
                (cardForIssue) =>
                  send({ type: 'DEFAULT_CARD_ISSUE', value: cardForIssue })
              " />
            <!--            <CreateCardIssueV2-->
            <!--              :bin="context.selectedBin"-->
            <!--              :card-tariff-slug="context.cardTariff"-->
            <!--              :is-auto-buy="context.isAutoBuy"-->
            <!--              @submit-form="-->
            <!--                (cardForIssue, promoCode) => {-->
            <!--                  send({ type: 'SET_PROMO_CODE', value: promoCode });-->
            <!--                  send({ type: 'DEFAULT_CARD_ISSUE', value: cardForIssue });-->
            <!--                }-->
            <!--              " />-->
          </div>
          <div v-if="step === CardCreateMachineState.VERIFICATION_SCALE_STEP">
            <VerificationProcess
              :is-custom-case="
                context.cardType === 'forAdv' && haveNeedVerificationAction
              "
              @success="send({ type: 'VERIFICATION_COMPLETED' })" />
          </div>

          <div
            v-if="
              step === CardCreateMachineState.APPROVE_CARD_STEP &&
              context.cardForIssue
            ">
            <CreateCardApprove
              :card-for-issue="context.cardForIssue"
              :promo-code-data="context.promoCodeData"
              @single-buy-success="handleSingleBuySuccess"
              @multi-buy-success="handleMultiBuySuccess" />
          </div>
          <div
            v-if="
              step === CardCreateMachineState.AUTO_BUY_PAYMENT_STEP &&
              context.cardForIssue
            ">
            <CreateCardAutoBuyPayment
              :card-for-issue="context.cardForIssue"
              :promo-code-data="context.promoCodeData"
              @auto-buy-success="handleAutoBuySuccess" />
          </div>

          <div
            v-if="
              step ===
                CardCreateMachineState.AUTO_BUY_PAYMENT_STEP_EXPERIMENT &&
              context.cardForIssue &&
              context.cardTariff
            ">
            <CreateCardAutoBuyPaymentV2
              :card-for-issue="context.cardForIssue"
              :promo-code-data="context.promoCodeData"
              @set-promo-code="
                (promoCodeData) =>
                  send({ type: 'SET_PROMO_CODE', value: promoCodeData ?? null })
              "
              @auto-buy-success="handleAutoBuySuccess" />
          </div>

          <div v-if="step === CardCreateMachineState.SUCCESS_STEP_EXPERIMENT">
            <CreateCardSuccessV2
              :card="context.resultCard || null"
              :auto-refill="autoRefillInfo"
              @close="closeHandle" />
          </div>
          <div v-if="step === CardCreateMachineState.SUCCESS_STEP">
            <CreateCardSuccess @confirm="confirmHandle" />
          </div>
        </UITransition>
      </div>
    </template>
  </UIFullScreenModal>
</template>
