import { computed, type Ref } from "vue";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { calcAmountWithReversedCommissionAndConversion } from "@/helpers/calcAmountWithReversedCommissionAndConversion";
import { prepareAccountBalance } from "@/helpers/account";
import { useUserStore } from "@/stores/user";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import type { TTariffResource } from "@/types/api/TTariffResource";
import type { TXUR } from "@/types/api/TXUR";
import { experiments } from "@/config/cards";
import {
  useSubscriptionsInfo,
  useTariffGet,
  useUserExchangeRatesGet,
  useUserTariff,
} from "@/composable";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import { isCardCodeResource } from "@/components/PromoCodeInput/usePromoCodeInput";
import { useSubscriptionsTariffsGet } from "@/composable/API/useSubscriptionsTariffsGet";
import { useSubscriptionPlusCardAutobuy1Experiment } from "@/composable/useSubscriptionPlusCardAutobuy1Experiment";
import { useUserBonusAccountGet } from "@/composable/API/useUserBonusAccountGet";

export const useCreateCardSummary = (
  cardForIssue: Ref<TCardForIssue>,
  selectedAccount: Ref<TUserAccountResource | undefined | null>,
  promoCodeData: Ref<TPromoCodeResource | null>
) => {
  const userStore = useUserStore();
  const { userTariffData, isFetching: isFetchingUserTariff } = useUserTariff();
  const { data: tariffsData } = useTariffGet();
  const { data: userBonusAccountData } = useUserBonusAccountGet();
  const { subscriptionsStatus } = useSubscriptionsInfo();
  const { data: subscriptionsTariffData } = useSubscriptionsTariffsGet();
  const { getValue } = useSubscriptionPlusCardAutobuy1Experiment();
  const { isActive: isAdvWithSubActive } = getValue();
  const { data: ratesData } = useUserExchangeRatesGet();

  const isUltima = computed(() => cardForIssue.value.type.includes("ultima"));

  const exchangeRates = computed<TXUR | null>(() => {
    return ratesData.value?.data ?? null;
  });

  // tariffs

  // general tariffs
  const generalTariffs = computed<TTariffResource[]>(() => {
    return tariffsData.value?.data ?? [];
  });

  // user specific tariffs
  const userTariffs = computed<TTariffResource[]>(() => {
    return userTariffData.data?.value?.data ?? [];
  });

  // current base tariff
  const generalTariff = computed<TTariffResource | undefined>(() => {
    return generalTariffs.value.find(
      (tariff) => tariff.slug === cardForIssue.value.type
    );
  });

  // current user tariff
  const userCardTariff = computed<TTariffResource | undefined>(() => {
    return userTariffs.value?.find(
      (tariff) => tariff.slug === cardForIssue.value.type
    );
  });

  const extraSmallTariffPrice = computed<number>(() => {
    const extraSmallTariff = subscriptionsTariffData.value?.data?.find(
      (subscriptionTariff) => subscriptionTariff.slug === "Extra Small"
    );
    const price = extraSmallTariff?.amount || "0";
    return parseFloat(price);
  });

  // card prices

  // base tariff monthly payment
  const generalTariffPayment = computed(() => {
    return parseFloat(generalTariff.value?.card_price || "0");
  });

  // user card tariff monthly payment
  const userCardTariffPayment = computed(() => {
    return parseFloat(userCardTariff.value?.card_price || "0");
  });

  // tariff payment with discount
  const userCardTariffPaymentWithDiscount = computed<number>(() => {
    const useSubscriptionsTariffPrice = !!(
      !isUltima.value &&
      !subscriptionsStatus.value &&
      isAdvWithSubActive.value
    );
    if (useSubscriptionsTariffPrice) {
      return extraSmallTariffPrice.value;
    }
    return calcDiscount(
      userCardTariffPayment.value,
      promoCodeDiscountPercent.value
    );
  });

  const promoCodeDiscountPercent = computed<number>(() => {
    if (
      promoCodeData.value &&
      isCardCodeResource(promoCodeData.value?.fields)
    ) {
      return (
        Number(promoCodeData.value?.fields?.card_buy_discount_percent) || 0
      );
    }
    return 0;
  });

  // display payments

  // displayed tariff payment
  const tariffPaymentWithDiscount = computed(() => {
    if (promoCodeDiscountPercent.value > 0) {
      return calcDiscount(
        userCardTariffPayment.value,
        promoCodeDiscountPercent.value
      );
    }
    return userCardTariffPayment.value;
  });

  // displayed crossed tariff payment
  const tariffPaymentCrossed = computed<number | null>(() => {
    if (promoCodeDiscountPercent.value > 0) {
      return userCardTariffPayment.value;
    }

    const haveUserTariffDifference =
      generalTariff.value?.card_price !== userCardTariff.value?.card_price;

    return haveUserTariffDifference ? generalTariffPayment.value : null;
  });

  // starting balance of the card

  // promo code card_bonus_amount
  const promoCodeBonusAmount = computed<number>(() => {
    if (
      promoCodeData.value &&
      isCardCodeResource(promoCodeData.value?.fields)
    ) {
      return Number(promoCodeData.value?.fields?.card_bonus_amount) || 0;
    }
    return 0;
  });

  const startingBalanceCrossed = computed(() => {
    const startBalance = cardForIssue.value.startBalance ?? 0;
    const minBalance = cardForIssue.value.minValue ?? 0;

    return startBalance < minBalance ? minBalance : startBalance;
  });

  const startingBalance = computed(() => {
    return startingBalanceCrossed.value + promoCodeBonusAmount.value;
  });

  // ? transaction fee display
  // transaction fee
  const transactionFee = computed(() => {
    return parseFloat(userCardTariff.value?.fee_transaction_amount || "0");
  });

  // crossed transaction fee
  const transactionFeeCrossed = computed(() => {
    if (
      generalTariff.value?.fee_transaction_amount !==
      userCardTariff.value?.fee_transaction_amount
    ) {
      return parseFloat(generalTariff.value?.fee_transaction_amount || "0");
    } else {
      return null;
    }
  });

  // ? top-up fee display

  const isFacebookCardsExperiment = computed(() => {
    return (
      cardForIssue.value.type === "facebook-cards" &&
      userStore.user?.experiments?.fbdeposit_discount2
    );
  });

  // top-up fee percent
  const feeTopUpPercent = computed<number>(() => {
    const feeTopUp = parseFloat(userCardTariff.value?.fee_topup ?? "0");
    if (isFacebookCardsExperiment.value) {
      const startBalance = cardForIssue.value.startBalance;
      const discountFeeTopUpPercent = Number(
        experiments?.facebookCards?.discountFeeTopUpPercent || "0"
      );
      const discountFeeTopUpFromUsd = Number(
        experiments?.facebookCards?.discountFeeTopUpFromUsd || "0"
      );
      if (
        startBalance &&
        discountFeeTopUpPercent &&
        startBalance >= discountFeeTopUpFromUsd
      ) {
        return feeTopUp - discountFeeTopUpPercent;
      }
    }
    return feeTopUp;
  });

  // top-up fee percent crossed
  const feeTopUpPercentCrossed = computed<number | null>(() => {
    if (generalTariff.value?.fee_topup !== userCardTariff.value?.fee_topup) {
      return parseFloat(generalTariff.value?.fee_topup || "0");
    } else {
      return null;
    }
  });

  const selectedAccountIsoCode = computed(() => {
    return (
      (selectedAccount.value &&
        getAccountCurrencyByCurrencyId(selectedAccount.value?.currency_id)
          ?.isoCode) ||
      IsoCodeNames.USD
    );
  });

  const conversionRate = computed<number>(() => {
    if (
      !exchangeRates.value ||
      !selectedAccountIsoCode.value ||
      !exchangeRates.value[selectedAccountIsoCode.value as IsoCodeNames] ||
      selectedAccountIsoCode.value === IsoCodeNames.USD
    ) {
      return 1;
    }
    return Number(
      exchangeRates.value[selectedAccountIsoCode.value as IsoCodeNames][
        IsoCodeNames.USD
      ]
    );
  });

  const total = computed<number>(() => {
    const cardsCount = cardForIssue.value?.count ?? 1;
    const startBalance = cardForIssue.value?.startBalance ?? 0;
    const cardPrice =
      userCardTariffPaymentWithDiscount.value / conversionRate.value;
    const feeTopUp = feeTopUpPercent.value;

    const amount = calcAmountWithReversedCommissionAndConversion(
      startBalance,
      conversionRate.value,
      feeTopUp,
      1
    );

    return (amount + cardPrice) * cardsCount;
  });

  const availableBonusBalanceForPurchase = computed<number>(() => {
    return Math.min(
      Number(userBonusAccountData.value?.data.balance ?? 0),
      userCardTariffPaymentWithDiscount.value
    );
  });

  const totalAmount = computed<number>(() => {
    return Number(
      prepareAccountBalance(
        total.value -
          availableBonusBalanceForPurchase.value / conversionRate.value,
        selectedAccountIsoCode.value,
        false,
        2
      )
    );
  });

  // calculate discounted price
  const calcDiscount = (price: number, discountPercent: number) => {
    return price - (price * discountPercent) / 100;
  };

  return {
    isFetching: computed(() => isFetchingUserTariff.value),
    userCardTariff,
    // payment
    tariffPaymentWithDiscount,
    tariffPaymentCrossed,
    promoCodeDiscountPercent,
    // starting balance
    startingBalance,
    startingBalanceCrossed,
    promoCodeBonusAmount,
    // top-up fee
    feeTopUpPercent,
    feeTopUpPercentCrossed,
    // total
    totalAmount,
    bonusAmount: availableBonusBalanceForPurchase,
    // transaction fee
    transactionFee,
    transactionFeeCrossed,
    // facebook experiment
    isFacebookCardsExperiment,
    extraSmallTariffPrice,
  };
};
