<script lang="ts" setup>
import { useCountrySets } from "@/composable/useCountrySets";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import VerificationSupportSection from "../VerificationSupportSection.vue";
import { ref } from "vue";
import { useExtendedVerificationEmail } from "@/composable/useExtendedVerificationEmail";
import { useUserStore } from "@/stores/user";
import VerificationProcess from "@/components/VerificationProcess/VerificationProcess.vue";

type Props = {
  isManagerHelpActive?: boolean;
};

const { isActive } = useCountrySets();
const { isExtendedVerificationEmail } = useExtendedVerificationEmail();
const { getUser: userStoreState } = useUserStore();

const props = withDefaults(defineProps<Props>(), {
  isManagerHelpActive: false,
});
const emit = defineEmits(["completed"]);
const isGateActive = ref(true);
const onVerificationCompleted = async () => {
  await userStoreState();
  emit("completed");
};
</script>

<template>
  <div class="flex flex-col">
    <div
      v-if="isGateActive && props.isManagerHelpActive"
      class="flex flex-col items-center justify-center mx-auto max-w-[29.125rem] mb-4">
      <div class="flex flex-col items-center max-w-[28.625rem]">
        <div class="outer-circle">
          <div class="inner-circle">
            <DynamicIcon
              class="w-[33px] h-[33px]"
              name="verify_outlined_green" />
          </div>
        </div>
        <div class="flex flex-col items-center w-full pb-6">
          <div class="font-medium text-6 leading-7 mb-4 text-center">
            {{ $t("verification.account-verification-header") }}
          </div>
          <div class="font-normal text-4.5 leading-6 mb-4 text-center">
            {{ $t("verification.account-verification-text") }}
          </div>
          <div class="w-full">
            <UIButton
              class="w-full"
              color="black"
              @click="isGateActive = false">
              {{ $t("verification.account-verification-button-text") }}
            </UIButton>
          </div>
          <div class="flex mt-4">
            <div
              class="font-normal text-4 leading-5 text-center text-fg-secondary">
              <span>
                {{ $t("verification.account-verification-partner-text") }}
              </span>
              &nbsp;
              <span class="cursor-pointer underline">Sumsub</span>
            </div>
          </div>
          <div class="pt-4">
            <DynamicIcon
              class="ml-auto float-right"
              name="powered-by-sumsub" />
          </div>
        </div>
      </div>
      <div class="flex flex-col">
        <div class="flex flex-row items-center my-10">
          <div>
            <DynamicIcon
              class="w-full my-4"
              name="dots-new-card" />
          </div>
          <div class="text-fg-secondary font-normal leading-4 text-3.5 px-2">
            {{ $t("2fa-settings.or") }}
          </div>
          <div>
            <DynamicIcon
              class="w-full my-4"
              name="dots-new-card" />
          </div>
        </div>
      </div>
      <div class="flex flex-col">
        <VerificationSupportSection
          :variant="
            isActive || isExtendedVerificationEmail ? 'compliance' : 'private'
          " />
      </div>
    </div>
    <VerificationProcess
      v-else
      @success="onVerificationCompleted" />
  </div>
</template>

<style lang="scss" scoped>
.outer-circle {
  @apply w-[66px] h-[66px] rounded-full flex items-center justify-center my-6;
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.08), 0 4px 4px 0 rgba(0, 0, 0, 0.07),
    0 9px 5px 0 rgba(0, 0, 0, 0.03), 0 15px 6px 0 rgba(0, 0, 0, 0.01);
}

.inner-circle {
  @apply w-[54px] h-[54px] rounded-full flex items-center justify-center;
  border: 1px solid rgba(0, 0, 0, 0.03);
  background: rgba(233, 248, 240, 1);
}
</style>
